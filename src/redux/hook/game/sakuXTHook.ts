import { useDispatch } from 'react-redux';
import { AppDispatch } from '../../store/store';
import { checkAnswer, clearFeedback, initializeGame, loadSakuXTGameConfig, loadSakuXTQuestions, nextQuestion, reset, setData, startTimer, stopTimer, updateTimer } from '../../reducers/game/SakuXTReducer';


export const useSakuXTHook = () => {
  const dispatch = useDispatch<AppDispatch>();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },

    // API calls
    loadGameConfig: (gameId: string) => {
      return dispatch(loadSakuXTGameConfig({ gameId }));
    },

    loadQuestions: (gameId: string, stage: number, competenceId: string) => {
      return dispatch(loadSakuXTQuestions({ gameId, stage, competenceId }));
    },

    // Game actions
    initializeGame: () => {
      dispatch(initializeGame());
    },

    nextQuestion: () => {
      dispatch(nextQuestion());
    },

    checkAnswer: () => {
      dispatch(checkAnswer());
    },

    clearFeedback: () => {
      dispatch(clearFeedback());
    },

    // Timer actions
    updateTimer: () => {
      dispatch(updateTimer());
    },

    startTimer: () => {
      dispatch(startTimer());
    },

    stopTimer: () => {
      dispatch(stopTimer());
    },

    reset: () => {
      dispatch(reset());
    },
  };

  return action;
};
