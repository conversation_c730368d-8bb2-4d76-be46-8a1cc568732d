import { useDispatch } from 'react-redux';
import { AppDispatch } from '../../store/store';
import {
  reset,
  setData,
  initializeGame,
  nextQuestion,
  checkAnswer,
  clearFeedback,
  updateTimer,
  startTimer,
  stopTimer,
  loadSakuLCGameConfig,
  loadSakuLCQuestions,
} from '../../reducers/game/sakuLCReducer';

export const useSakuLCHook = () => {
  const dispatch = useDispatch<AppDispatch>();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },

    // API calls
    loadGameConfig: (gameId: string) => {
      return dispatch(loadSakuLCGameConfig({ gameId }));
    },

    loadQuestions: (gameId: string, stage: number, competenceId: string) => {
      return dispatch(loadSakuLCQuestions({ gameId, stage, competenceId }));
    },

    // Game actions
    initializeGame: () => {
      dispatch(initializeGame());
    },

    nextQuestion: () => {
      dispatch(nextQuestion());
    },

    checkAnswer: () => {
      dispatch(checkAnswer());
    },

    clearFeedback: () => {
      dispatch(clearFeedback());
    },

    // Timer actions
    updateTimer: () => {
      dispatch(updateTimer());
    },

    startTimer: () => {
      dispatch(startTimer());
    },

    stopTimer: () => {
      dispatch(stopTimer());
    },

    reset: () => {
      dispatch(reset());
    },
  };

  return action;
};
