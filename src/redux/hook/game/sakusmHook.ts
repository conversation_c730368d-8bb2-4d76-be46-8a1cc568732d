import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../store/store';
import {
  reset,
  setData,
  startGame,
  markHintUsed,
  loadSakuSMGameConfig,
  loadSakuSMQuestions
} from '../../reducers/game/sakuSMReducer';

export const useSakuSMHook = () => {
  const dispatch = useDispatch<AppDispatch>();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    reset: () => {
      dispatch(reset());
    },
    markHintUsed: (questionId: string) => {
      dispatch(markHintUsed(questionId));
    },

    // API actions
    loadGameConfig: (gameId: string) => {
      return dispatch(loadSakuSMGameConfig({ gameId }));
    },
    loadQuestions: (gameId: string, stage: number, competenceId: string) => {
      return dispatch(loadSakuSMQuestions({ gameId, stage, competenceId }));
    },
  };

  return action;
};
