import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Dimensions,
  ImageBackground,
} from 'react-native';

const {width, height} = Dimensions.get('window');

interface ModelDoneLevelProps {
  visible: boolean;
  currentGem: number;
  currentCup: number;
  gemAdd: number;
  cupAdd: number;
  onNextLevel: () => void;
}

const ModelDoneLevel = ({
  visible,
  onNextLevel,
  currentGem,
  currentCup,
  gemAdd,
  cupAdd,
}: ModelDoneLevelProps) => {
  const onClose = () => {};
  const handleRestart = () => {};
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Hiển thị icon thời gian hoặc icon thua cuộc */}
          <View style={styles.iconContainer}>
            <View style={styles.pointContainer}>
              <View style={[styles.icon, {top: 6}]}>
                <Image source={require('../assets/big_gem.png')}></Image>
              </View>
              <Text style={styles.textIcon}>
                {currentGem}+{gemAdd}
              </Text>
            </View>
            <View style={[styles.pointContainer, {marginTop: 36}]}>
              <View style={[styles.icon, {top: 0}]}>
                <Image source={require('../assets/big_cup.png')}></Image>
              </View>
              <Text style={styles.textIcon}>
                {currentCup}+{cupAdd}
              </Text>
            </View>
          </View>

          {/* Hình ảnh con chim buồn */}
          <View style={styles.birdContainer}>
            <Image source={require('../assets/winner_bird.png')} />
          </View>
          {/* Nút restart */}
          {/* thêm background image cho nút start */}
          <TouchableOpacity
            style={styles.restartButton}
            onPress={handleRestart}>
            <Image
              source={require('../assets/next_button.png')}
              style={styles.restartButton}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.69)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContent: {
    width: width * 0.93,
    height: height * 0.76,
    backgroundColor: 'rgba(112, 90, 64, 0.96)', // Màu nền nâu như trong ảnh
    padding: 20,
    marginBottom: 20,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  iconContainer: {
    alignItems: 'center',
    marginTop: 20,
  },

  birdContainer: {
    marginVertical: 30,
    position: 'relative',
  },

  restartButton: {
    width: 230,
    height: 90,
    alignItems: 'center',
    justifyContent: 'center',
  },

  pointContainer: {
    position: 'relative',
    backgroundColor: 'white',
    width: 130,
    marginLeft: 24,
    height: 32,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    position: 'absolute',
    zIndex: 1,
    left: 0,
    transform: [{translateX: -10}, {translateY: -3}],
    alignItems: 'center',
    justifyContent: 'center',
    width: 25,
    height: 25,
  },
  textIcon: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default ModelDoneLevel;
