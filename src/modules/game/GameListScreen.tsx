import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {GameDA} from './gameDA';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {RootScreen} from '../../router/router';

const {width, height} = Dimensions.get('window');

// Hàm để tính toán kích thước responsive
const wp = (percentage: number) => {
  return width * (percentage / 100);
};

const hp = (percentage: number) => {
  return height * (percentage / 100);
};

// Màu sắc mặc định cho các game
const defaultColors = [
  '#FF9FF3', // Hồng nhạt
  '#1B1464', // Xanh đậm
  '#FFC312', // Vàng
  '#C4E538', // <PERSON>anh lá
  '#FDA7DF', // Hồng
  '#12CBC4', // Xanh ngọc
  '#9980FA', // Tím
  '#B53471', // Đỏ đậm
  '#1289A7', // Xanh dương
  '#F79F1F', // Cam
];

const GameListScreen = () => {
  const navigation = useNavigation<any>();
  const [games, setGames] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Lấy danh sách game từ API khi component mount
  useEffect(() => {
    fetchGames();
  }, []);

  // Hàm lấy danh sách game từ API
  const fetchGames = async () => {
    try {
      setLoading(true);
      const gameDA = new GameDA();
      const response = await gameDA.getGameList();

      if (response && response.data) {
        // Xử lý dữ liệu game từ API
        const processedGames = response.data.map(
          (game: any, index: number) => ({
            id: game.Id,
            name: game.Name,
            color: defaultColors[index % defaultColors.length], // Luân phiên sử dụng các màu
            icon: game.Image, // URL hình ảnh từ API
          }),
        );

        setGames(processedGames);
      } else {
        setError('Không thể tải danh sách game');
      }
    } catch (err) {
      console.error('Lỗi khi lấy danh sách game:', err);
      setError('Đã xảy ra lỗi khi tải danh sách game');
    } finally {
      setLoading(false);
    }
  };

  // Hàm xử lý khi nhấn vào game
  const handleGamePress = (game: any) => {
    if (!game) {
      showSnackbar({
        message: 'Trò chơi này đang được phát triển',
        status: ComponentStatus.INFOR,
      });
      return;
    }
    // @ts-ignore
    navigation.navigate(RootScreen.HomeGame, {gameId: game.id});
  };

  // Render từng game item
  const renderGameItem = (game: any, _index: number) => {
    return (
      <TouchableOpacity
        key={game.id}
        style={styles.gameItem}
        onPress={() => handleGamePress(game)}
        activeOpacity={0.7}>
        <View style={[styles.iconContainer]}>
          {game.icon && (
            <Image
              source={{
                uri: `https://redis.ktxgroup.com.vn/api/file/img/${game.icon}`,
              }}
              style={styles.gameIcon}
              resizeMode="contain"
            />
          )}
          {/* Chú thích: Nếu không có icon từ API, sẽ hiển thị màu nền */}
        </View>
        <Text style={styles.gameName}>{game.name}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Background Image - Sẽ được thay thế bằng hình thực tế */}
      <View style={styles.backgroundImageContainer}>
        <Image
          source={require('./assets/game_background.png')}
          style={styles.backgroundImage}
          resizeMode="cover"
        />
        {/* Chú thích: Cần thêm hình nền vào thư mục assets */}
        {/* Hình nền sẽ có cảnh biển, mây, và hai nhân vật đang cầm cờ cá */}
      </View>

      {/* Nội dung chính */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1B1464" />
          <Text style={styles.loadingText}>Đang tải danh sách game...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchGames}>
            <Text style={styles.retryButtonText}>Thử lại</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}>
          {/* Grid các game */}
          <View style={styles.gameGrid}>
            {games.map((game, index) => renderGameItem(game, index))}
          </View>
        </ScrollView>
      )}

      <View style={{flexDirection: 'row', justifyContent: 'center'}}>
        {/* <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            navigation.navigate(RootScreen.StartSakuTB);
          }}>
          <Text style={styles.retryButtonText}>Xaku Tìm bạn</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            navigation.navigate(RootScreen.StartSakuLC);
          }}>
          <Text style={styles.retryButtonText}>Xaku Luyện công</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            navigation.navigate(RootScreen.StartMGHH);
          }}>
          <Text style={styles.retryButtonText}>Mảnh ghép hoàn hảo</Text>
        </TouchableOpacity> */}
        {/* <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            navigation.navigate(RootScreen.StartSakuXT);
          }}>
          <Text style={styles.retryButtonText}>SukuXayto</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            navigation.navigate(RootScreen.StartSakuSM);
          }}>
          <Text style={styles.retryButtonText}>Saku Săn mồi</Text>
        </TouchableOpacity> */}
        {/* <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            navigation.navigate(RootScreen.StartSakuTC);
          }}>
          <Text style={styles.retryButtonText}>Saku Truyền cành</Text>
        </TouchableOpacity> */}
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            navigation.navigate(RootScreen.StartVCNV);
          }}>
          <Text style={styles.retryButtonText}>Vuợt chướng ngại vât</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FBE8C5', // Màu nền giống trong ảnh (màu cát/bãi biển)
  },
  backgroundImageContainer: {
    position: 'absolute',
    width: '100%',
    height: hp(25), // Chiếm khoảng 40% chiều cao màn hình
    top: 0,
    backgroundColor: '#A7D7F9', // Màu xanh nhạt cho phần background (tạm thời)
  },
  backgroundImage: {
    width: '100%',
    height: 250,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingTop: hp(24), // Để nội dung hiển thị dưới phần background
    paddingBottom: hp(5),
    paddingHorizontal: wp(5),
  },
  gameGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: hp(2),
  },
  gameItem: {
    width: wp(28), // Khoảng 3 items trên một hàng với khoảng cách
    marginBottom: hp(4),
    alignItems: 'center',
  },
  iconContainer: {
    width: wp(20),
    height: wp(20),
    marginBottom: hp(1),
    borderRadius: wp(10),
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  gameIcon: {
    width: '100%',
    height: '100%',
    borderRadius: wp(7),
  },
  gameName: {
    textAlign: 'center',
    color: '#112164',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
    maxWidth: wp(28),
  },
  // Styles cho loading và error
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: hp(10),
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: hp(10),
  },
  errorText: {
    fontSize: 16,
    color: '#B53471',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#1B1464',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default GameListScreen;
