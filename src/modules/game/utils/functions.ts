function replaceObjectById(newObj: any, array: any) {
  const cloneArray = [...array];
  const index = array.findIndex((item: any) => item.id === newObj.id);
  if (index !== -1) {
    cloneArray[index] = newObj;
  }
  return cloneArray;
}

// Kiểm tra list object đưa vào có đúng thứ tự tăng dần không
const checkPositionOrder = (arr: any[]) => {
  if (arr.length <= 1) {
    return true;
  }

  for (let i = 0; i < arr.length - 1; i++) {
    if (arr[i].position > arr[i + 1].position) {
      return false;
    }
  }

  return true;
};

export {replaceObjectById, checkPositionOrder};
