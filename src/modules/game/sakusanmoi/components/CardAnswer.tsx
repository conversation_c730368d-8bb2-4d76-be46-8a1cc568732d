import React, {use, useEffect} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {SakuSmAnswer} from '../../../../redux/reducers/game/sakuSMReducer';

interface CardAnswerProps {
  answer: SakuSmAnswer;
  index: number;
  side: 'left' | 'right';
  onClick: (status: 'correct' | 'wrong' | null) => void;
  allowChoose: boolean;
}

const CardAnswer = ({
  answer,
  index,
  side,
  onClick,
  allowChoose,
}: CardAnswerProps) => {
  const [isCheck, setIsCheck] = React.useState<'correct' | 'wrong' | null>(
    null,
  );

  useEffect(() => {
    return () => {
      setIsCheck(null);
    };
  }, [answer]);

  const handleClick = () => {
    if (!allowChoose) return;
    if (answer.isTrue) {
      setIsCheck('correct');
      onClick('correct');
    } else {
      setIsCheck('wrong');
      onClick('wrong');
    }
  };
  const getImage = (index: number) => {
    switch (index) {
      case 0:
        return require('../assets/worm_1.png');
      case 1:
        return require('../assets/worm_2.png');
      case 2:
        return require('../assets/worm_3.png');
      case 3:
        return require('../assets/worm_4.png');
      default:
        return require('../assets/worm_1.png');
    }
  };
  const image = getImage(index);
  return (
    <View
      style={{position: 'relative', alignItems: 'center', marginVertical: 6}}>
      <TouchableOpacity
        style={[
          styles.card,
          {
            backgroundColor:
              isCheck === 'correct'
                ? '#27e844'
                : isCheck === 'wrong'
                ? '#fc3030'
                : '#FCF8E8',
          },
        ]}
        onPress={allowChoose ? handleClick : undefined}>
        <Text style={[styles.text]}>{answer.text}</Text>
      </TouchableOpacity>
      <View
        style={[
          {position: 'absolute', top: -10},
          side === 'left' ? {left: 10} : {right: 10},
        ]}>
        <Image source={image} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    width: '80%',
    minHeight: 40,
    backgroundColor: '#FCF8E8',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginVertical: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardError: {
    backgroundColor: '#fc3030',
  },
  cardSuccess: {
    backgroundColor: '#27e844',
  },
  text: {
    padding: 8,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
  },
});

export default CardAnswer;
