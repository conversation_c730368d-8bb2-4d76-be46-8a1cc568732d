import {createSlice, current} from '@reduxjs/toolkit';
import {question} from '../../data/question';
import {MiniQuestion, Question} from '../../models/models';

interface State {
  currentQuestion: Question | null;
  currentMiniQuestion: MiniQuestion | null;
  listMiniQuestion: MiniQuestion[] | null;
}

const initialState: State = {
  currentQuestion: null,
  listMiniQuestion: null,
};

export const GameSlice = createSlice({
  name: 'Game',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    startGame: (state: State) => {
      state.currentQuestion = question;
      state.listMiniQuestion = question.miniQuestions;
    },
  },
});

export const {setData, startGame} = GameSlice.actions;

export default GameSlice.reducer;
